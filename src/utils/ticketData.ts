import type { UserRole } from "@/contexts/RoleContext";

export interface Task {
  id: string;
  title: string;
  status: "Open" | "In Progress" | "Completed";
  assignee: string;
  department: string;
}

export interface Ticket {
  id: string;
  title: string;
  category: string;
  priority: "Low" | "Medium" | "High" | "Critical";
  status: string;
  createdDate: string;
  slaRemaining: string;
  requester: string;
  assignedTo: string;
  department: string;
  confidential?: boolean;
  tasks?: Task[];
}

// All available tickets in the system
export const allTickets: Ticket[] = [
  // End User's tickets
  {
    id: "TKT-001",
    title: "Login issues with SSO authentication",
    category: "Authentication",
    priority: "High",
    status: "In Progress",
    createdDate: "2024-01-15",
    slaRemaining: "2 hours",
    requester: "<PERSON>",
    assignedTo: "IT Support",
    department: "Marketing",
    tasks: [
      { id: "TSK-001", title: "Analyze error logs", status: "Completed", assignee: "<PERSON>", department: "IT Support" },
      { id: "TSK-002", title: "Reproduce the issue in staging", status: "In Progress", assignee: "<PERSON>", department: "IT Support" },
      { id: "TSK-003", title: "Implement fix", status: "Open", assignee: "Jane Smith", department: "Development" }
    ]
  },
  {
    id: "TKT-002",
    title: "Database performance optimization needed",
    category: "Performance",
    priority: "Medium",
    status: "Pending Review",
    createdDate: "2024-01-14",
    slaRemaining: "1 day",
    requester: "John Smith",
    assignedTo: "Development",
    department: "Marketing"
  },
  // Back Office Agent's tickets
  {
    id: "TKT-003",
    title: "Network connectivity issues in building A",
    category: "Infrastructure",
    priority: "Critical",
    status: "Awaiting Info",
    createdDate: "2024-01-13",
    slaRemaining: "4 hours",
    requester: "Mike Wilson",
    assignedTo: "IT Support",
    department: "IT Support",
    tasks: [
      { id: "TSK-004", title: "Check router configuration", status: "In Progress", assignee: "Mike Wilson", department: "IT Support" },
      { id: "TSK-005", title: "Verify physical connections", status: "Open", assignee: "Mike Wilson", department: "IT Support" }
    ]
  },
  {
    id: "TKT-004",
    title: "Software license renewal request",
    category: "Procurement",
    priority: "Medium",
    status: "Pending Approval",
    createdDate: "2024-01-12",
    slaRemaining: "3 days",
    requester: "Sarah Johnson",
    assignedTo: "IT Support",
    department: "IT Support"
  },
  // Department Manager's tickets
  {
    id: "TKT-005",
    title: "Budget approval for cloud migration",
    category: "Finance",
    priority: "High",
    status: "Pending Approval",
    createdDate: "2024-01-15",
    slaRemaining: "1 day",
    requester: "Emily Davis",
    assignedTo: "IT Support",
    department: "IT Support"
  },
  {
    id: "TKT-006",
    title: "Security audit findings remediation",
    category: "Security",
    priority: "Critical",
    status: "In Progress",
    createdDate: "2024-01-10",
    slaRemaining: "6 hours",
    requester: "Alex Thompson",
    assignedTo: "IT Support",
    department: "IT Administration",
    confidential: true
  },
  // Other department tickets
  {
    id: "TKT-007",
    title: "HR system integration issues",
    category: "Integration",
    priority: "Medium",
    status: "Open",
    createdDate: "2024-01-11",
    slaRemaining: "2 days",
    requester: "Jessica Brown",
    assignedTo: "Development",
    department: "Human Resources"
  },
  {
    id: "TKT-008",
    title: "Payroll system downtime investigation",
    category: "Infrastructure",
    priority: "Critical",
    status: "In Progress",
    createdDate: "2024-01-09",
    slaRemaining: "1 hour",
    requester: "David Lee",
    assignedTo: "IT Support",
    department: "Finance",
    confidential: true
  },
  {
    id: "TKT-009",
    title: "Customer data export request",
    category: "Data Request",
    priority: "Low",
    status: "Completed",
    createdDate: "2024-01-08",
    slaRemaining: "Completed",
    requester: "Maria Garcia",
    assignedTo: "Development",
    department: "Customer Service"
  }
];

// Filter tickets based on user role and permissions
export function getTicketsForRole(role: UserRole, userName: string, userDepartment: string): Record<string, Ticket[]> {
  let visibleTickets: Ticket[] = [];

  switch (role) {
    case 'end-user':
      // End users can only see their own tickets
      visibleTickets = allTickets.filter(ticket => ticket.requester === userName);
      break;

    case 'back-office-agent':
      // Back office agents can see (same as department managers):
      // - All tickets in their department
      // - Tickets assigned to their department
      // - Their own tickets
      visibleTickets = allTickets.filter(ticket =>
        ticket.department === userDepartment ||
        ticket.assignedTo === userDepartment ||
        ticket.requester === userName
      );
      break;

    case 'department-manager':
      // Department managers can see:
      // - All tickets in their department
      // - Tickets assigned to their department
      // - Some confidential tickets in their domain
      visibleTickets = allTickets.filter(ticket => 
        ticket.department === userDepartment ||
        ticket.assignedTo === userDepartment ||
        ticket.requester === userName
      );
      break;

    case 'administrator':
      // Administrators can see all tickets
      visibleTickets = [...allTickets];
      break;
  }

  // Organize tickets by status for different tabs
  return {
    "raised-by-me": visibleTickets.filter(ticket => ticket.requester === userName),
    "for-approval": visibleTickets.filter(ticket => 
      ticket.status === "Pending Approval" && 
      (role === 'department-manager' || role === 'administrator')
    ),
    "to-work-on": visibleTickets.filter(ticket => 
      ticket.assignedTo === userDepartment && 
      !["Completed", "Closed", "Pending Approval"].includes(ticket.status)
    ),
    "overdue": visibleTickets.filter(ticket => 
      ticket.slaRemaining.includes("overdue") || 
      (ticket.slaRemaining.includes("hour") && parseInt(ticket.slaRemaining) <= 2)
    ),
    "completed": visibleTickets.filter(ticket => 
      ["Completed", "Closed"].includes(ticket.status)
    ),
    "on-hold": visibleTickets.filter(ticket => ticket.status === "On Hold"),
    "rejected": visibleTickets.filter(ticket => ticket.status === "Rejected"),
    "all": visibleTickets
  };
}

// Get available tabs based on user role
export function getAvailableTabsForRole(role: UserRole): string[] {
  switch (role) {
    case 'end-user':
      return ['raised-by-me', 'on-hold', 'rejected'];

    case 'back-office-agent':
      return ['raised-by-me', 'for-approval', 'to-work-on', 'overdue', 'completed'];

    case 'department-manager':
      return ['raised-by-me', 'for-approval', 'to-work-on', 'overdue', 'completed'];

    case 'administrator':
      return ['all', 'for-approval', 'to-work-on', 'overdue', 'completed'];

    default:
      return ['raised-by-me'];
  }
}

// Get tab display names
export function getTabDisplayName(tab: string): string {
  const displayNames: Record<string, string> = {
    'raised-by-me': 'Raised by Me',
    'for-approval': 'For Approval',
    'to-work-on': 'To Work On',
    'overdue': 'Overdue',
    'completed': 'Completed',
    'on-hold': 'On Hold',
    'rejected': 'Rejected',
    'all': 'All Tickets'
  };
  
  return displayNames[tab] || tab;
}
