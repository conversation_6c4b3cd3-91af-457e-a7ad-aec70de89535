import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Star, MessageSquare, ThumbsUp } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface FeedbackDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ticketId: string;
  ticketTitle: string;
}

export function FeedbackDialog({ open, onOpenChange, ticketId, ticketTitle }: FeedbackDialogProps) {
  const { toast } = useToast();
  const [rating, setRating] = useState<number>(0);
  const [hoveredRating, setHoveredRating] = useState<number>(0);
  const [satisfaction, setSatisfaction] = useState<string>("");
  const [comments, setComments] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const satisfactionOptions = [
    { value: "very-satisfied", label: "Very Satisfied", emoji: "😊" },
    { value: "satisfied", label: "Satisfied", emoji: "🙂" },
    { value: "neutral", label: "Neutral", emoji: "😐" },
    { value: "dissatisfied", label: "Dissatisfied", emoji: "🙁" },
    { value: "very-dissatisfied", label: "Very Dissatisfied", emoji: "😞" }
  ];

  const handleSubmit = async () => {
    if (rating === 0) {
      toast({
        title: "Rating Required",
        description: "Please provide a rating before submitting your feedback.",
        variant: "destructive",
      });
      return;
    }

    if (!satisfaction) {
      toast({
        title: "Satisfaction Level Required",
        description: "Please select your satisfaction level.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "Feedback Submitted",
        description: "Thank you for your feedback! Your input helps us improve our service.",
      });

      // Reset form
      setRating(0);
      setSatisfaction("");
      setComments("");
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Submission Failed",
        description: "Failed to submit feedback. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setRating(0);
    setSatisfaction("");
    setComments("");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ThumbsUp className="h-5 w-5 text-blue-600" />
            Ticket Feedback
          </DialogTitle>
          <DialogDescription>
            Please share your experience with the resolution of ticket <strong>{ticketId}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Ticket Title */}
          <div className="p-3 bg-muted/50 rounded-lg">
            <p className="text-sm font-medium text-muted-foreground mb-1">Ticket:</p>
            <p className="text-sm">{ticketTitle}</p>
          </div>

          {/* Star Rating */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Overall Rating *</Label>
            <div className="flex items-center gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  className="p-1 hover:scale-110 transition-transform"
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  onClick={() => setRating(star)}
                >
                  <Star
                    className={`h-8 w-8 ${
                      star <= (hoveredRating || rating)
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-gray-300"
                    }`}
                  />
                </button>
              ))}
              {rating > 0 && (
                <span className="ml-2 text-sm text-muted-foreground">
                  {rating} out of 5 stars
                </span>
              )}
            </div>
          </div>

          {/* Satisfaction Level */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Satisfaction Level *</Label>
            <RadioGroup value={satisfaction} onValueChange={setSatisfaction}>
              <div className="grid grid-cols-1 gap-2">
                {satisfactionOptions.map((option) => (
                  <div key={option.value} className="flex items-center space-x-3">
                    <RadioGroupItem value={option.value} id={option.value} />
                    <Label 
                      htmlFor={option.value} 
                      className="flex items-center gap-2 cursor-pointer flex-1 p-2 rounded hover:bg-muted/50"
                    >
                      <span className="text-lg">{option.emoji}</span>
                      <span>{option.label}</span>
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          </div>

          {/* Additional Comments */}
          <div className="space-y-3">
            <Label htmlFor="comments" className="text-base font-medium">
              Additional Comments
            </Label>
            <Textarea
              id="comments"
              placeholder="Please share any additional feedback about your experience, what went well, or areas for improvement..."
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              className="min-h-[100px] resize-none"
            />
            <p className="text-xs text-muted-foreground">
              Your feedback is valuable and helps us improve our service quality.
            </p>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleCancel} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <MessageSquare className="h-4 w-4 mr-2 animate-spin" />
                Submitting...
              </>
            ) : (
              <>
                <ThumbsUp className="h-4 w-4 mr-2" />
                Submit Feedback
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
