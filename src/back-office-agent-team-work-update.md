# Back Office Agent Team Work Enhancement

## Overview
Updated the back-office agent role to have the same team work capabilities as department managers, including the ability to assign and reassign tickets.

## Changes Made

### 1. Enhanced Back Office Agent Permissions (`src/contexts/RoleContext.tsx`)

**Added the following permissions to back-office-agent role:**
- `view-team-tickets` - View tickets across the team
- `assign-tickets` - Assign tickets to team members
- `reassign-tickets` - Change ticket assignments
- `approve-tickets` - Approve ticket requests
- `reject-tickets` - Reject ticket requests
- `manage-team-workload` - Manage team workload distribution
- `manage-team` - Access team management actions
- `edit-team-tickets` - Edit team tickets

**Before:**
```typescript
permissions: [
  'create-ticket', 
  'view-own-tickets', 
  'view-assigned-tickets',
  'edit-assigned-tickets',
  'update-ticket-status',
  'add-worklogs',
  'view-department-tickets'
]
```

**After:**
```typescript
permissions: [
  'create-ticket', 
  'view-own-tickets', 
  'view-assigned-tickets',
  'edit-assigned-tickets',
  'update-ticket-status',
  'add-worklogs',
  'view-department-tickets',
  'view-team-tickets',
  'assign-tickets',
  'reassign-tickets',
  'approve-tickets',
  'reject-tickets',
  'manage-team-workload',
  'manage-team',
  'edit-team-tickets'
]
```

### 2. Updated Available Tabs (`src/utils/ticketData.ts`)

**Made back-office agent tabs identical to department manager:**

**Before:**
```typescript
case 'back-office-agent':
  return ['raised-by-me', 'to-work-on', 'completed'];
```

**After:**
```typescript
case 'back-office-agent':
  return ['raised-by-me', 'for-approval', 'to-work-on', 'overdue', 'completed'];
```

### 3. Enhanced Ticket Visibility (`src/utils/ticketData.ts`)

**Updated ticket filtering to match department manager access:**

**Before:**
```typescript
case 'back-office-agent':
  // Limited access with confidential restrictions
  visibleTickets = allTickets.filter(ticket => 
    ticket.requester === userName || 
    (ticket.assignedTo === userDepartment && !ticket.confidential) ||
    ticket.department === userDepartment
  );
```

**After:**
```typescript
case 'back-office-agent':
  // Same access as department managers
  visibleTickets = allTickets.filter(ticket => 
    ticket.department === userDepartment ||
    ticket.assignedTo === userDepartment ||
    ticket.requester === userName
  );
```

## New Capabilities for Back Office Agents

### 1. **Full Team Work Tab Access**
- **Raised by Me**: Tickets created by the agent
- **For Approval**: Tickets requiring approval
- **To Work On**: Tickets assigned to work on
- **Overdue**: Tickets past their SLA
- **Completed**: Resolved tickets

### 2. **Ticket Assignment Powers**
- **Assign Unassigned Tickets**: Can assign tickets to team members
- **Reassign Tickets**: Can change existing ticket assignments
- **Self-Assignment**: Can assign tickets to themselves
- **Team Management**: Access to team management actions

### 3. **Enhanced Visibility**
- **Department Tickets**: See all tickets in their department
- **Team Tickets**: View tickets across the team
- **Assignment History**: Track ticket assignment changes

### 4. **Approval Workflow**
- **Approve Tickets**: Can approve ticket requests
- **Reject Tickets**: Can reject tickets with reasons
- **Workload Management**: Manage team workload distribution

## UI Features Available

### Assignment Interface
- **Assignment Buttons**: "Assign to me" and "Assign to someone" buttons
- **Assignment Dialog**: Full assignment dialog with:
  - Team member selection dropdown
  - Assignment notes
  - Previous assignee information
  - Ticket details display

### Team Actions Menu
- **Team Management**: Access to team management dropdown
- **Bulk Operations**: Team-level bulk operations
- **Workload Distribution**: Team workload management tools

## Testing Instructions

### 1. Switch to Back Office Agent Role
- Use the role switcher in the navigation
- Select "Back Office Agent"

### 2. Navigate to Team Work
- Go to the Team Work page
- Verify all 5 tabs are visible: Raised by Me, For Approval, To Work On, Overdue, Completed

### 3. Test Assignment Functionality
- Find an unassigned ticket
- Click the "Assign to someone" button (UserPlus icon)
- Verify the assignment dialog opens
- Select a team member and add a note
- Submit the assignment

### 4. Test Self-Assignment
- Find an unassigned ticket
- Click the "Assign to me" button (CheckCircle icon)
- Verify the ticket gets assigned to the current user

### 5. Test Team Management
- Look for the "Team Actions" dropdown in the header
- Verify team management options are available

## Impact Summary

**Back office agents now have:**
✅ **Same permissions as department managers**
✅ **Full team work tab access**
✅ **Ticket assignment capabilities**
✅ **Team management access**
✅ **Enhanced ticket visibility**
✅ **Approval workflow participation**

This enhancement allows back office agents to effectively manage team workload and ticket assignments, providing the same level of control as department managers while maintaining appropriate role boundaries.
