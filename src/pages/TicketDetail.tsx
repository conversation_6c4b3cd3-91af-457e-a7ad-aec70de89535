import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  ArrowLeft,
  Edit,
  Clock,
  User,
  Calendar,
  Tag,
  Paperclip,
  MessageSquare,
  CheckSquare,
  Plus,
  Play,
  Pause,
  Eye,
  Timer,
  AlertTriangle,
  Zap,
  Activity,
  Building,
  MapPin,
  Users,
  <PERSON>older<PERSON><PERSON>,
  Flag,
  Star
} from "lucide-react";
import { TaskDetailDialog } from "@/components/TaskDetailDialog";
import { CreateTaskDialog } from "@/components/CreateTaskDialog";
import { HoldTicketDialog } from "@/components/HoldTicketDialog";
import { WorklogTimer } from "@/components/WorklogTimer";
import { EditConflictAlert } from "@/components/EditConflictAlert";
import { MentionInput, MentionRenderer } from "@/components/MentionInput";
import { FeedbackDialog } from "@/components/FeedbackDialog";
import { useToast } from "@/hooks/use-toast";
import { useRole } from "@/contexts/RoleContext";

// Enhanced TicketDetail interface with all create ticket fields
interface TicketDetail {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  subCategory: string;
  assignee: string;
  requester: string;
  department: string;
  assignedToGroup: string;
  createdDate: string;
  updatedDate: string;
  tags: string[];
  attachments: string[];
  slaHours: number;
  totalSlaHours: number;
  slaStatus: "good" | "warning" | "critical";
  ticketType: "service-request" | "service-incident";
  onBehalfOfType: "team" | "individual";
  onBehalfOfEntity: string;
  location: string;
  project?: string;
  initialWorkLog?: string;
}

interface TimelineEvent {
  id: string;
  type: "status_change" | "comment" | "assignment" | "worklog";
  user: string;
  timestamp: string;
  content: string;
  metadata?: any;
}

interface Comment {
  id: string;
  user: string;
  timestamp: string;
  content: string;
  visibility: "public" | "private";
  group: string;
  replies?: Comment[];
}

// Updated Subtask interface with new status options
interface Subtask {
  id: string;
  title: string;
  description: string;
  status: "Assigned" | "In Progress" | "Requested Information" | "Awaiting Approval" | "Approved" | "Rejected" | "Resolved" | "Hold";
  assignee: string;
  priority: "Low" | "Medium" | "High" | "Critical";
  timeLogged: number;
  estimatedHours: number;
  group: string;
  department: string;
  createdDate: string;
  assignmentType?: "department" | "individual"; // Add this field
}

interface WorklogEntry {
  id: string;
  user: string;
  date: string;
  timeSpent: number;
  description: string;
}

// Enhanced mock ticket with real ticket data
const mockTicket: TicketDetail = {
  id: "TKT-001",
  title: "Requesting for AWS AI Practitioner Reimbursement Fee",
  description: "Hi team, Could you please review my invoice regarding AWS AI Practitioner Certificate and reimburse accordingly. Thank you, Charan Teja.",
  status: "Resolved",
  priority: "Medium",
  category: "Talent Development",
  subCategory: "Certifications & Training",
  assignee: "Vineeth Emmadi",
  requester: "Charan Teja",
  department: "Talent Management",
  assignedToGroup: "Finance",
  createdDate: "2024-12-26",
  updatedDate: "2025-02-07",
  tags: ["Reimbursement", "AWS", "Certification"],
  attachments: ["AWS AIP Payment Invoice.pdf", "AWS Certified AI Practitioner certificate.pdf", "AWS Certified AI Practitioner.pdf"],
  slaHours: 18,
  totalSlaHours: 72,
  slaStatus: "good",
  ticketType: "service-request",
  onBehalfOfType: "individual",
  onBehalfOfEntity: "Charan Teja",
  location: "Hyderabad",
  project: "",
  initialWorkLog: "AWS AI Practitioner certification reimbursement request submitted with all required documents."
};

const mockTimeline: TimelineEvent[] = [
  {
    id: "1",
    type: "status_change",
    user: "Vineeth Emmadi", 
    timestamp: "2025-02-07 09:00",
    content: "Finance: Status changed to 'In Progress' - Processing reimbursement"
  },
  {
    id: "2",
    type: "assignment",
    user: "Poulami Bose",
    timestamp: "2024-12-26 16:30", 
    content: "HR: Approved and assigned to Finance department"
  },
  {
    id: "3",
    type: "comment",
    user: "Poulami Bose",
    timestamp: "2024-12-26 16:25",
    content: "HR: Final approval completed - documents verified and approved"
  },
  {
    id: "4",
    type: "comment",
    user: "Arvind Koilada",
    timestamp: "2024-12-26 15:45",
    content: "HR: Document verification completed - all documents are clear and valid"
  },
  {
    id: "5", 
    type: "assignment",
    user: "Lokanath Myneni",
    timestamp: "2024-12-26 14:30",
    content: "Management: BU Head approval completed - Approved for reimbursement processing"
  },
  {
    id: "6",
    type: "assignment",
    user: "System",
    timestamp: "2024-12-26 10:15",
    content: "System: Assigned to BU Head (Lokanath Myneni) for approval"
  },
  {
    id: "7",
    type: "status_change",
    user: "System", 
    timestamp: "2024-12-26 10:00",
    content: "System: Ticket created - AWS AI Practitioner reimbursement request"
  }
];

const mockComments: Comment[] = [
  // Management/BU Head Comments
  {
    id: "1",
    user: "Lokanath Myneni",
    timestamp: "2024-12-26 14:30",
    content: "Approved",
    visibility: "private",
    group: "Management"
  },
  
  // HR Department Comments
  {
    id: "2",
    user: "Arvind Koilada",
    timestamp: "2024-12-26 15:45",
    content: "Hi Poulami, The documents have been verified and are clear. Kindly request your approval to proceed with the payment process. Thanks & regards, Arvind",
    visibility: "private",
    group: "HR",
    replies: [
      {
        id: "2-1",
        user: "Poulami Bose",
        timestamp: "2024-12-26 16:25",
        content: "Thanks Arvind. Documents look good. Proceeding with approval.",
        visibility: "private",
        group: "HR"
      }
    ]
  },
  {
    id: "3",
    user: "Poulami Bose",
    timestamp: "2024-12-26 16:30",
    content: "Approved and assigned to finance",
    visibility: "private",
    group: "HR"
  },
  
  // Finance Department Comments
  {
    id: "4",
    user: "Vineeth Emmadi",
    timestamp: "2025-02-07 09:00",
    content: "Started processing the reimbursement. Expected completion by end of week.",
    visibility: "private",
    group: "Finance"
  },
  
  // Public/General Comments
  {
    id: "5",
    user: "Charan Teja",
    timestamp: "2024-12-26 10:05",
    content: "All required documents have been attached. Please let me know if any additional information is needed.",
    visibility: "public",
    group: "Talent Management"
  }
];

// Updated mock data with proper departmental tasks and workflow
const mockSubtasks: Subtask[] = [
  {
    id: "TSK-001",
    title: "BU Head Approval Required",
    description: "Initial ticket requires Business Unit Head approval before proceeding to HR verification. Please review the AWS AI Practitioner certification reimbursement request and provide approval.",
    status: "Resolved",
    assignee: "Lokanath Myneni",
    priority: "Medium",
    timeLogged: 0.25,
    estimatedHours: 0.5,
    group: "Management",
    department: "Management",
    createdDate: "2024-12-26",
    assignmentType: "individual"
  },
  {
    id: "TSK-002", 
    title: "HR Document Verification and Approval",
    description: "BU Head has approved the reimbursement request. Please verify all submitted documents (AWS certificate and payment invoice) and provide HR approval for payment processing.",
    status: "Resolved",
    assignee: "HR Team",
    priority: "Medium",
    timeLogged: 0.75,
    estimatedHours: 1,
    group: "HR",
    department: "Human Resources",
    createdDate: "2024-12-26",
    assignmentType: "department"
  },
  {
    id: "TSK-003",
    title: "Process Reimbursement Payment",
    description: "HR has verified and approved all documents. Please process the AWS AI Practitioner certification reimbursement payment for Charan Teja as per company policy.",
    status: "In Progress", 
    assignee: "Finance Team",
    priority: "Medium",
    timeLogged: 0.5,
    estimatedHours: 2,
    group: "Finance",
    department: "Finance",
    createdDate: "2024-12-26",
    assignmentType: "department"
  }
];

// Updated worklogs by department
const mockWorklogs: WorklogEntry[] = [
  {
    id: "WL-001",
    user: "Vineeth Emmadi",
    date: "2025-02-07",
    timeSpent: 0.5,
    description: "Finance: Reviewed reimbursement request and initiated payment processing"
  },
  {
    id: "WL-002", 
    user: "Poulami Bose",
    date: "2024-12-26",
    timeSpent: 0.25,
    description: "HR: Final HR approval and assignment to Finance team"
  },
  {
    id: "WL-003",
    user: "Arvind Koilada", 
    date: "2024-12-26",
    timeSpent: 0.5,
    description: "HR: Document verification - verified AWS certificate and payment invoice"
  },
  {
    id: "WL-004",
    user: "Lokanath Myneni",
    date: "2024-12-26", 
    timeSpent: 0.25,
    description: "Management: BU Head review and approval for AWS certification reimbursement"
  }
];

export default function TicketDetail() {
  const { ticketId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { currentUser } = useRole();
  const [newComment, setNewComment] = useState("");
  const [commentVisibility, setCommentVisibility] = useState<"public" | "private">("public");
  const [newWorklog, setNewWorklog] = useState({ timeSpent: "", description: "" });
  const [selectedTask, setSelectedTask] = useState<Subtask | null>(null);
  const [taskDetailOpen, setTaskDetailOpen] = useState(false);
  const [createTaskOpen, setCreateTaskOpen] = useState(false);
  const [holdDialogOpen, setHoldDialogOpen] = useState(false);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);
  const [tasks, setTasks] = useState<Subtask[]>(mockSubtasks);
  const [mentionComment, setMentionComment] = useState("");
  const [commentMentions, setCommentMentions] = useState<any[]>([]);
  
  // Mock current user data - Finance team member viewing all tasks
  const currentUserGroup = "Finance";
  const currentUserName = "Vineeth Emmadi";

  // Check if current user can edit task status
  const canEditTaskStatus = currentUser?.role === "back-office-agent" || currentUser?.role === "administrator";

  const totalTimeLogged = mockWorklogs.reduce((total, entry) => total + entry.timeSpent, 0);
  
  // SLA calculation
  const slaPercentage = (mockTicket.slaHours / mockTicket.totalSlaHours) * 100;
  const getSlaColor = () => {
    if (mockTicket.slaStatus === "critical") return "text-red-600";
    if (mockTicket.slaStatus === "warning") return "text-yellow-600";
    return "text-green-600";
  };

  const getTimelineIcon = (type: string) => {
    switch (type) {
      case "status_change": return Activity;
      case "assignment": return User;
      case "comment": return MessageSquare;
      case "worklog": return Clock;
      default: return Activity;
    }
  };
  
  // Show all tasks regardless of group for this view (back-office can see all)
  const filteredTasks = tasks; // Show all tasks

  // Filter comments based on visibility and user group
  const filteredComments = mockComments.filter(comment => 
    comment.visibility === "public" || 
    (comment.visibility === "private" && comment.group === currentUserGroup)
  );

  const handleTaskClick = (task: Subtask) => {
    setSelectedTask(task);
    setTaskDetailOpen(true);
  };

  const handleCreateTask = (newTask: Subtask) => {
    setTasks(prev => [...prev, newTask]);
    toast({
      title: "Task Created",
      description: `Task "${newTask.title}" has been created successfully.`,
    });
  };

  const handleHoldTicket = (holdData: any) => {
    toast({
      title: "Ticket On Hold",
      description: `Ticket will be reactivated on ${holdData.reactivationDate.toLocaleDateString()}`,
    });
  };

  const handleLogWork = (worklogEntry: any) => {
    toast({
      title: "Work Logged",
      description: `${worklogEntry.duration} minutes logged successfully`,
    });
  };

  const handleMentionComment = (content: string, mentions: any[]) => {
    toast({
      title: "Comment Posted",
      description: mentions.length > 0 ? `Comment posted with ${mentions.length} mentions` : "Comment posted",
    });
    setMentionComment("");
    setCommentMentions([]);
  };

  // Enhanced status color function for new status options
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Assigned": return "bg-blue-100 text-blue-800 border-blue-200";
      case "In Progress": return "bg-purple-100 text-purple-800 border-purple-200";
      case "Requested Information": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Awaiting Approval": return "bg-orange-100 text-orange-800 border-orange-200";
      case "Approved": return "bg-green-100 text-green-800 border-green-200";
      case "Rejected": return "bg-red-100 text-red-800 border-red-200";
      case "Resolved": return "bg-emerald-100 text-emerald-800 border-emerald-200";
      case "Hold": return "bg-gray-100 text-gray-800 border-gray-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "Critical": return "destructive";
      case "High": return "destructive";
      case "Medium": return "default";
      case "Low": return "secondary";
      default: return "default";
    }
  };

  const getTicketTypeColor = (type: string) => {
    switch (type) {
      case "service-request": return "bg-blue-100 text-blue-800 border-blue-200";
      case "service-incident": return "bg-orange-100 text-orange-800 border-orange-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getTicketTypeLabel = (type: string) => {
    switch (type) {
      case "service-request": return "Service Request";
      case "service-incident": return "Service Incident";
      default: return type;
    }
  };

  // Get department color for visual distinction
  const getDepartmentColor = (department: string) => {
    switch (department) {
      case "Management": return "bg-red-50 border-l-red-400";
      case "Human Resources": return "bg-green-50 border-l-green-400";
      case "Finance": return "bg-blue-50 border-l-blue-400";
      case "Talent Management": return "bg-purple-50 border-l-purple-400";
      default: return "bg-gray-50 border-l-gray-400";
    }
  };

  // Handle task status change
  const handleTaskStatusChange = (taskId: string, newStatus: Subtask["status"]) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, status: newStatus } : task
    ));
    
    toast({
      title: "Task Status Updated",
      description: `Task status changed to "${newStatus}"`,
    });
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="border-b">
        {/* Edit Conflict Alert */}
        <EditConflictAlert
          ticketId={mockTicket.id}
          currentUserId="current-user"
          onRefresh={() => window.location.reload()}
        />
        
        <div className="flex items-center gap-4 p-4">
          <Button variant="ghost" size="sm" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div className="flex-1">
            <h1 className="text-xl font-semibold">{mockTicket.title}</h1>
            <div className="flex items-center gap-2 mt-1">
              <p className="text-sm text-muted-foreground">{mockTicket.id}</p>
              <div className={`flex items-center gap-1 text-sm ${getSlaColor()}`}>
                <Timer className="h-3 w-3" />
                SLA: {mockTicket.slaHours}h remaining ({Math.round(slaPercentage)}%)
              </div>
              {mockTicket.slaStatus === "critical" && (
                <Badge variant="destructive" className="text-xs">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  SLA at Risk
                </Badge>
              )}
            </div>
          </div>
          <Button>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          {/* Show Feedback button only for end users on resolved tickets */}
          {currentUser?.role === 'end-user' && mockTicket.status === 'Resolved' && (
            <Button variant="outline" onClick={() => setFeedbackDialogOpen(true)}>
              <Star className="h-4 w-4 mr-2" />
              Provide Feedback
            </Button>
          )}
          <Button variant="outline" onClick={() => setHoldDialogOpen(true)}>
            <Pause className="h-4 w-4 mr-2" />
            Hold
          </Button>
        </div>
      </div>

      <div className="flex">
        {/* Main Content */}
        <div className="flex-1 p-6 pr-0">
          <div className="space-y-6 pr-6">
            {/* Ticket Info - Enhanced with all fields */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-3">
                    {/* Primary badges */}
                    <div className="flex items-center gap-2 flex-wrap">
                      <Badge variant={getPriorityColor(mockTicket.priority) as any}>
                        <Flag className="h-3 w-3 mr-1" />
                        {mockTicket.priority}
                      </Badge>
                      <Badge variant="default">
                        <Activity className="h-3 w-3 mr-1" />
                        {mockTicket.status}
                      </Badge>
                      <div className={`inline-flex items-center px-2 py-1 rounded-md border text-sm font-medium ${getTicketTypeColor(mockTicket.ticketType)}`}>
                        <CheckSquare className="h-3 w-3 mr-1" />
                        {getTicketTypeLabel(mockTicket.ticketType)}
                      </div>
                    </div>

                    {/* Ticket metadata in organized sections */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                      {/* Assignment Information */}
                      <div className="space-y-2 p-3 rounded-lg bg-blue-50 border-l-4 border-blue-400">
                        <h4 className="font-medium text-blue-900 flex items-center gap-2">
                          <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                            <User className="h-3.5 w-3.5 text-blue-600" />
                          </div>
                          Assignment
                        </h4>
                        <div className="space-y-1 text-blue-700">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-blue-500" />
                            <span>Assigned to {mockTicket.assignee}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-blue-500" />
                            <span>Group: {mockTicket.assignedToGroup}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Building className="h-4 w-4 text-blue-500" />
                            <span>Department: {mockTicket.department}</span>
                          </div>
                        </div>
                      </div>

                      {/* Request Information */}
                      <div className="space-y-2 p-3 rounded-lg bg-green-50 border-l-4 border-green-400">
                        <h4 className="font-medium text-green-900 flex items-center gap-2">
                          <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                            <MessageSquare className="h-3.5 w-3.5 text-green-600" />
                          </div>
                          Request Details
                        </h4>
                        <div className="space-y-1 text-green-700">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-green-500" />
                            <span>Requested by {mockTicket.requester}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-green-500" />
                            <span>On behalf of {mockTicket.onBehalfOfEntity} ({mockTicket.onBehalfOfType})</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-green-500" />
                            <span>Location: {mockTicket.location}</span>
                          </div>
                        </div>
                      </div>

                      {/* Categorization */}
                      <div className="space-y-2 p-3 rounded-lg bg-purple-50 border-l-4 border-purple-400">
                        <h4 className="font-medium text-purple-900 flex items-center gap-2">
                          <div className="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center">
                            <Tag className="h-3.5 w-3.5 text-purple-600" />
                          </div>
                          Categorization
                        </h4>
                        <div className="space-y-1 text-purple-700">
                          <div className="flex items-center gap-2">
                            <Tag className="h-4 w-4 text-purple-500" />
                            <span>{mockTicket.category}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Tag className="h-4 w-4 text-purple-500" />
                            <span>{mockTicket.subCategory}</span>
                          </div>
                          {mockTicket.project && (
                            <div className="flex items-center gap-2">
                              <FolderOpen className="h-4 w-4 text-purple-500" />
                              <span>Project: {mockTicket.project}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Dates */}
                    <div className="flex items-center gap-6 text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Created {mockTicket.createdDate}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Updated {mockTicket.updatedDate}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium mb-2">Description</h3>
                    <p className="text-muted-foreground leading-relaxed">{mockTicket.description}</p>
                  </div>

                  {mockTicket.initialWorkLog && (
                    <div>
                      <h3 className="font-medium mb-2">Initial Work Log</h3>
                      <div className="bg-muted/50 p-3 rounded-md">
                        <p className="text-sm text-muted-foreground">{mockTicket.initialWorkLog}</p>
                      </div>
                    </div>
                  )}

                  {mockTicket.attachments.length > 0 && (
                    <div>
                      <h3 className="font-medium mb-2">Attachments</h3>
                      <div className="space-y-1">
                        {mockTicket.attachments.map((file) => (
                          <div key={file} className="flex items-center gap-2 text-sm">
                            <Paperclip className="h-3 w-3" />
                            <span className="text-blue-600 hover:underline cursor-pointer">{file}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {mockTicket.tags.length > 0 && (
                    <div>
                      <h3 className="font-medium mb-2">Tags</h3>
                      <div className="flex flex-wrap gap-1">
                        {mockTicket.tags.map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Tasks Section - Enhanced with departmental workflow */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Workflow Tasks ({filteredTasks.length})</h3>
                  <Button size="sm" onClick={() => setCreateTaskOpen(true)}>
                    <Plus className="h-4 w-4 mr-1" />
                    Add Task
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {filteredTasks.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <CheckSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No tasks found for this ticket.</p>
                    <Button variant="outline" size="sm" className="mt-2" onClick={() => setCreateTaskOpen(true)}>
                      Create First Task
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredTasks.map((task) => (
                      <div key={task.id} className={`bg-card border-y relative shadow-sm hover:shadow-md transition-shadow duration-300 border-l-4 ${getDepartmentColor(task.department)}`}>
                        {/* Left cutout */}
                        <div className="absolute top-1/2 -translate-y-1/2 -left-4 w-8 h-8 rounded-full bg-background"></div>
                        {/* Right cutout */}
                        <div className="absolute top-1/2 -translate-y-1/2 -right-4 w-8 h-8 rounded-full bg-background"></div>
                        
                        <div className="flex">
                          {/* Main Task Info */}
                          <div className="flex-1 p-4 space-y-3">
                            <div className="flex items-start justify-between">
                              <div className="flex items-center gap-3">
                                <CheckSquare className="h-5 w-5 text-primary mt-1" />
                                <div>
                                  <div className="text-base font-semibold text-card-foreground">{task.title}</div>
                                  <div className="text-sm text-muted-foreground mt-1">{task.description}</div>
                                </div>
                              </div>
                            </div>
                            
                            <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm text-muted-foreground pl-8">
                              <div className="flex items-center gap-2">
                                <Badge variant="secondary">{task.department}</Badge>
                              </div>
                              <div className="flex items-center gap-2">
                                <User className="h-3.5 w-3.5" />
                                <span>{task.assignee}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Calendar className="h-3.5 w-3.5" />
                                <span>{task.createdDate}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <AlertTriangle className="h-3.5 w-3.5" />
                                <span>{task.priority} Priority</span>
                              </div>
                            </div>
                          </div>
                          
                          {/* Dashed Separator */}
                          <div className="w-px bg-transparent" style={{
                            backgroundImage: `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23e2e8f0' stroke-width='2' stroke-dasharray='6%2c 10' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e")`
                          }}></div>

                          {/* Status and Actions */}
                          <div className="w-48 p-4 flex flex-col items-center justify-center space-y-3">
                            {/* Status Display/Dropdown */}
                            {canEditTaskStatus ? (
                              <Select
                                value={task.status}
                                onValueChange={(value: Subtask["status"]) => handleTaskStatusChange(task.id, value)}
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue>
                                    <div className={`inline-flex items-center px-2 py-1 rounded-md border text-xs font-medium ${getStatusColor(task.status)}`}>
                                      {task.status}
                                    </div>
                                  </SelectValue>
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Assigned">
                                    <div className={`inline-flex items-center px-2 py-1 rounded-md border text-xs font-medium ${getStatusColor("Assigned")}`}>
                                      Assigned
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="In Progress">
                                    <div className={`inline-flex items-center px-2 py-1 rounded-md border text-xs font-medium ${getStatusColor("In Progress")}`}>
                                      In Progress
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="Requested Information">
                                    <div className={`inline-flex items-center px-2 py-1 rounded-md border text-xs font-medium ${getStatusColor("Requested Information")}`}>
                                      Requested Information
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="Awaiting Approval">
                                    <div className={`inline-flex items-center px-2 py-1 rounded-md border text-xs font-medium ${getStatusColor("Awaiting Approval")}`}>
                                      Awaiting Approval
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="Approved">
                                    <div className={`inline-flex items-center px-2 py-1 rounded-md border text-xs font-medium ${getStatusColor("Approved")}`}>
                                      Approved
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="Rejected">
                                    <div className={`inline-flex items-center px-2 py-1 rounded-md border text-xs font-medium ${getStatusColor("Rejected")}`}>
                                      Rejected
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="Resolved">
                                    <div className={`inline-flex items-center px-2 py-1 rounded-md border text-xs font-medium ${getStatusColor("Resolved")}`}>
                                      Resolved
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="Hold">
                                    <div className={`inline-flex items-center px-2 py-1 rounded-md border text-xs font-medium ${getStatusColor("Hold")}`}>
                                      Hold
                                    </div>
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            ) : (
                              <div className={`inline-flex items-center px-2 py-1 rounded-md border text-sm font-medium w-full justify-center ${getStatusColor(task.status)}`}>
                                {task.status}
                              </div>
                            )}
                            
                            <div className="text-center text-muted-foreground">
                              <div className="text-lg font-bold text-card-foreground">{task.timeLogged}h</div>
                              <div className="text-xs">of {task.estimatedHours}h logged</div>
                            </div>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="w-full"
                              onClick={() => handleTaskClick(task)}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Right Sidebar - Keep existing sidebar content */}
        <div className="w-96 border-l bg-muted/10">
          <Tabs defaultValue="timeline" className="w-full">
            <TabsList className={`grid w-full m-4 mb-0 ${currentUser?.role === 'end-user' ? 'grid-cols-2' : 'grid-cols-3'}`}>
              <TabsTrigger value="timeline">Timeline</TabsTrigger>
              {currentUser?.role !== 'end-user' && (
                <TabsTrigger value="worklog">Worklog</TabsTrigger>
              )}
              <TabsTrigger value="comments">Comments</TabsTrigger>
            </TabsList>

            <TabsContent value="timeline" className="p-4 space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Activity Timeline</h3>
                <Badge variant="outline" className="text-xs">
                  {mockTimeline.length} events
                </Badge>
              </div>
              <div className="space-y-4">
                {mockTimeline.map((event, index) => {
                  const Icon = getTimelineIcon(event.type);
                  return (
                    <div key={event.id} className="flex gap-3">
                      <div className="flex flex-col items-center">
                        <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                          <Icon className="h-4 w-4" />
                        </div>
                        {index < mockTimeline.length - 1 && (
                          <div className="w-px h-8 bg-border mt-2" />
                        )}
                      </div>
                      <div className="flex-1 space-y-1 pb-4">
                        <p className="text-sm">{event.content}</p>
                        <p className="text-xs text-muted-foreground">
                          {event.user} • {event.timestamp}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </TabsContent>

            {currentUser?.role !== 'end-user' && (
              <TabsContent value="worklog" className="p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Work Log</h3>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    Total: {totalTimeLogged}h logged
                  </div>
                </div>

                <div className="space-y-3">
                  {mockWorklogs.map((entry) => (
                    <div key={entry.id} className="p-3 border rounded-lg space-y-2 hover:bg-muted/50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium">{entry.user}</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm font-medium text-blue-600">
                          <Clock className="h-3 w-3" />
                          {entry.timeSpent}h
                        </div>
                      </div>
                      <p className="text-sm">{entry.description}</p>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        {entry.date}
                      </div>
                    </div>
                  ))}
                </div>

                <Separator />

                <div className="space-y-3">
                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Log New Work
                  </h4>
                  <div className="space-y-2">
                    <Input
                      placeholder="Time spent (hours)"
                      value={newWorklog.timeSpent}
                      onChange={(e) => setNewWorklog(prev => ({ ...prev, timeSpent: e.target.value }))}
                    />
                    <Textarea
                      placeholder="Describe the work done..."
                      value={newWorklog.description}
                      onChange={(e) => setNewWorklog(prev => ({ ...prev, description: e.target.value }))}
                      className="min-h-16"
                    />
                    <div className="flex gap-2">
                      <Button size="sm" className="flex-1">
                        <Play className="h-3 w-3 mr-1" />
                        Log & Continue
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Pause className="h-3 w-3 mr-1" />
                        Log & Pause
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>
            )}

            <TabsContent value="comments" className="p-4 space-y-4">
              <h3 className="font-medium">Comments</h3>
              <div className="space-y-4">
                {filteredComments.map((comment) => (
                  <div key={comment.id} className="space-y-2">
                    <div className="flex items-start gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {comment.user.split(" ").map(n => n[0]).join("")}
                        </AvatarFallback>
                      </Avatar>
                        <div className="flex-1 space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">{comment.user}</span>
                          <Badge 
                            variant={comment.visibility === "private" ? "secondary" : "outline"}
                            className="text-xs"
                          >
                            {comment.visibility}
                          </Badge>
                          <span className="text-xs text-muted-foreground">{comment.timestamp}</span>
                        </div>
                        <div className="text-sm">
                          <MentionRenderer 
                            content={comment.content} 
                            mentions={[]} // Would come from comment data
                          />
                        </div>
                      </div>
                    </div>
                    
                    {comment.replies && (
                      <div className="ml-8 space-y-2">
                        {comment.replies.map((reply) => (
                          <div key={reply.id} className="flex items-start gap-2">
                            <Avatar className="h-5 w-5">
                              <AvatarFallback className="text-xs">
                                {reply.user.split(" ").map(n => n[0]).join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 space-y-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xs font-medium">{reply.user}</span>
                                <span className="text-xs text-muted-foreground">{reply.timestamp}</span>
                              </div>
                              <p className="text-xs">{reply.content}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <Separator className="my-4" />
              
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Button
                    variant={commentVisibility === "public" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCommentVisibility("public")}
                  >
                    Public
                  </Button>
                  <Button
                    variant={commentVisibility === "private" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCommentVisibility("private")}
                  >
                    Private
                  </Button>
                </div>
                
                <MentionInput
                  value={mentionComment}
                  onChange={(value, mentions) => {
                    setMentionComment(value);
                    setCommentMentions(mentions);
                  }}
                  onSubmit={handleMentionComment}
                  placeholder={`Add a ${commentVisibility} comment... (Type @ to mention users)`}
                  users={[]} // Would come from team members data
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Hold Ticket Dialog */}
      <HoldTicketDialog
        open={holdDialogOpen}
        onOpenChange={setHoldDialogOpen}
        ticketId={mockTicket.id}
        onHoldTicket={handleHoldTicket}
      />

      {/* Task Detail Dialog */}
      <TaskDetailDialog
        open={taskDetailOpen}
        onOpenChange={setTaskDetailOpen}
        task={selectedTask}
        userGroup={currentUserGroup}
      />

      {/* Create Task Dialog */}
      <CreateTaskDialog
        open={createTaskOpen}
        onOpenChange={setCreateTaskOpen}
        ticketId={mockTicket.id}
        onCreateTask={handleCreateTask}
      />

      {/* Feedback Dialog - Only for end users on resolved tickets */}
      {currentUser?.role === 'end-user' && mockTicket.status === 'Resolved' && (
        <FeedbackDialog
          open={feedbackDialogOpen}
          onOpenChange={setFeedbackDialogOpen}
          ticketId={mockTicket.id}
          ticketTitle={mockTicket.title}
        />
      )}
    </div>
  );
}