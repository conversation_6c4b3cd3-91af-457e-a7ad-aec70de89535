import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, CheckCircle, Clock, XCircle, Pause } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Subtask {
  id: string;
  title: string;
  status: "Assigned" | "In Progress" | "Requested Information" | "Awaiting Approval" | "Approved" | "Rejected" | "Resolved" | "Hold";
  assignee: string;
  priority: "Low" | "Medium" | "High" | "Critical";
  department: string;
}

interface TicketStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ticketId: string;
  currentStatus: string;
  tasks: Subtask[];
  onStatusChange: (newStatus: string, reason: string) => void;
}

const ticketStatuses = [
  { value: "Open", label: "Open", icon: Clock, color: "bg-blue-100 text-blue-800" },
  { value: "In Progress", label: "In Progress", icon: Clock, color: "bg-yellow-100 text-yellow-800" },
  { value: "Awaiting Information", label: "Awaiting Information", icon: AlertTriangle, color: "bg-orange-100 text-orange-800" },
  { value: "On Hold", label: "On Hold", icon: Pause, color: "bg-gray-100 text-gray-800" },
  { value: "Resolved", label: "Resolved", icon: CheckCircle, color: "bg-green-100 text-green-800" },
  { value: "Rejected", label: "Rejected", icon: XCircle, color: "bg-red-100 text-red-800" },
];

export function TicketStatusDialog({ 
  open, 
  onOpenChange, 
  ticketId, 
  currentStatus, 
  tasks, 
  onStatusChange 
}: TicketStatusDialogProps) {
  const { toast } = useToast();
  const [selectedStatus, setSelectedStatus] = useState(currentStatus);
  const [reason, setReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if ticket can be resolved based on task statuses
  const canResolveTicket = () => {
    const incompleteTasks = tasks.filter(task => 
      task.status !== "Resolved" && task.status !== "Rejected"
    );
    return incompleteTasks.length === 0;
  };

  // Get incomplete tasks for display
  const getIncompleteTasks = () => {
    return tasks.filter(task => 
      task.status !== "Resolved" && task.status !== "Rejected"
    );
  };

  const handleSubmit = async () => {
    if (!selectedStatus) {
      toast({
        title: "Status Required",
        description: "Please select a status for the ticket.",
        variant: "destructive",
      });
      return;
    }

    // Validate resolution attempt
    if (selectedStatus === "Resolved" && !canResolveTicket()) {
      toast({
        title: "Cannot Resolve Ticket",
        description: "All tasks must be completed before resolving the ticket.",
        variant: "destructive",
      });
      return;
    }

    if (!reason.trim()) {
      toast({
        title: "Reason Required",
        description: "Please provide a reason for the status change.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      onStatusChange(selectedStatus, reason);
      
      toast({
        title: "Status Updated",
        description: `Ticket ${ticketId} status changed to ${selectedStatus}`,
      });

      // Reset form
      setReason("");
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update ticket status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setSelectedStatus(currentStatus);
    setReason("");
    onOpenChange(false);
  };

  const incompleteTasks = getIncompleteTasks();
  const isResolutionBlocked = selectedStatus === "Resolved" && !canResolveTicket();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-600" />
            Change Ticket Status
          </DialogTitle>
          <DialogDescription>
            Update the status of ticket <strong>{ticketId}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Current Status */}
          <div className="p-3 bg-muted/50 rounded-lg">
            <p className="text-sm font-medium text-muted-foreground mb-1">Current Status:</p>
            <Badge className="text-sm">{currentStatus}</Badge>
          </div>

          {/* New Status Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">New Status *</Label>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Select new status" />
              </SelectTrigger>
              <SelectContent>
                {ticketStatuses.map((status) => {
                  const Icon = status.icon;
                  return (
                    <SelectItem key={status.value} value={status.value}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <span>{status.label}</span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Resolution Validation Alert */}
          {isResolutionBlocked && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p className="font-medium">Cannot resolve ticket with incomplete tasks:</p>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    {incompleteTasks.map((task) => (
                      <li key={task.id}>
                        <strong>{task.title}</strong> - Status: {task.status}
                      </li>
                    ))}
                  </ul>
                  <p className="text-sm">All tasks must be "Resolved" or "Rejected" before the ticket can be resolved.</p>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Task Status Summary */}
          {tasks.length > 0 && (
            <div className="space-y-3">
              <Label className="text-base font-medium">Task Status Summary</Label>
              <div className="p-3 border rounded-lg space-y-2">
                <div className="text-sm text-muted-foreground">
                  Total Tasks: {tasks.length} | 
                  Completed: {tasks.filter(t => t.status === "Resolved").length} | 
                  In Progress: {tasks.filter(t => t.status === "In Progress").length} |
                  Other: {tasks.filter(t => t.status !== "Resolved" && t.status !== "In Progress").length}
                </div>
                {incompleteTasks.length > 0 && (
                  <div className="text-sm">
                    <p className="font-medium text-orange-600 mb-1">Incomplete Tasks:</p>
                    {incompleteTasks.map((task) => (
                      <div key={task.id} className="flex items-center justify-between text-xs bg-orange-50 p-2 rounded">
                        <span>{task.title}</span>
                        <Badge variant="outline" className="text-xs">{task.status}</Badge>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Reason */}
          <div className="space-y-3">
            <Label htmlFor="reason" className="text-base font-medium">
              Reason for Status Change *
            </Label>
            <Textarea
              id="reason"
              placeholder="Please provide a reason for changing the ticket status..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="min-h-[80px] resize-none"
            />
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleCancel} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={isSubmitting || isResolutionBlocked}
          >
            {isSubmitting ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Update Status
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
