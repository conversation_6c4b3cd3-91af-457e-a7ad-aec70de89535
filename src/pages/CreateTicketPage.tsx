import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Plus,
  Upload,
  Paperclip,
  X,
  Save,
  FileText,
  Trash2,
  Pencil,
  Calendar as CalendarIcon,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

// New interface for a single task
interface Task {
  id: string;
  title: string;
  description: string;
  assignedTo: string; // Group assignment
  department: string; // Department assignment
}

// Attachment and Draft storage interfaces
interface AttachmentMetadata {
  name: string;
  size: number;
  type: string;
}

interface TicketDraft {
  id: string;
  title: string;
  description: string;
  project: string;
  category: string;
  subCategory: string;
  priority: string;
  onBehalfOf: string;
  onBehalfOfType: "team" | "individual";
  location: string;
  ticketType: "service-request" | "service-incident";
  requiredByDate?: string; // Add optional date
  attachments: AttachmentMetadata[];
  tasks: Task[];
  workLog: string;
  createdAt: string;
  updatedAt: string;
}

export default function CreateTicketPage() {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    project: "",
    category: "",
    subCategory: "",
    priority: "",
    onBehalfOf: "",
    onBehalfOfType: "individual" as "team" | "individual",
    location: "",
    ticketType: "service-request" as "service-request" | "service-incident",
    attachments: [] as File[],
    tasks: [] as Task[],
    workLog: "",
    requiredByDate: undefined as Date | undefined,
  });

  const [drafts, setDrafts] = useState<TicketDraft[]>([]);
  const [showDrafts, setShowDrafts] = useState(false);
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [currentTaskData, setCurrentTaskData] = useState({ title: "", description: "", assignedTo: "", department: "" });

  // Load drafts from localStorage on component mount
  useEffect(() => {
    const savedDrafts = localStorage.getItem('ticketDrafts');
    if (savedDrafts) {
      setDrafts(JSON.parse(savedDrafts));
    }
  }, []);

  const projects = [
    "Website Redesign",
    "Mobile App Development", 
    "Data Migration",
    "Security Audit",
    "API Integration",
    "Infrastructure Upgrade",
    "Customer Portal"
  ];

  const categories = [
    "Bug",
    "Feature Request",
    "Infrastructure",
    "Security",
    "Documentation",
    "Performance",
    "Access Request",
    "Hardware",
    "Software"
  ];

  const subCategories: Record<string, string[]> = {
    "Bug": ["UI/UX Bug", "Functional Bug", "Performance Bug", "Security Bug"],
    "Feature Request": ["New Feature", "Enhancement", "Integration", "Customization"],
    "Infrastructure": ["Server", "Network", "Database", "Cloud Services"],
    "Security": ["Access Control", "Vulnerability", "Compliance", "Audit"],
    "Documentation": ["User Guide", "Technical Docs", "Process Documentation"],
    "Performance": ["Speed", "Memory", "Database Performance", "Load Issues"],
    "Access Request": ["System Access", "Permission Change", "Account Creation"],
    "Hardware": ["Desktop", "Laptop", "Server Hardware", "Network Equipment"],
    "Software": ["Installation", "Upgrade", "License", "Configuration"]
  };

  const priorities = ["Low", "Medium", "High", "Critical"];

  const teams = [
    "Development Team",
    "Infrastructure Team", 
    "Security Team",
    "Support Team",
    "QA Team",
    "DevOps Team"
  ];

  const individuals = [
    "John Smith",
    "Sarah Johnson",
    "Mike Wilson",
    "Emily Davis",
    "Alex Thompson",
    "Jessica Brown",
    "David Lee",
    "Maria Garcia"
  ];

  const groups = [
    "IT Support",
    "Development",
    "Infrastructure", 
    "Security",
    "Quality Assurance",
    "DevOps",
    "Database Administration"
  ];

  const handleSubmit = (isDraft: boolean = false) => {
    const requiredFields = ['title', 'description', 'project', 'category', 'subCategory', 'priority', 'onBehalfOf', 'location', 'ticketType'];

    if (!isDraft) {
      const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);

      if (missingFields.length > 0) {
        toast({
          title: "Validation Error",
          description: `Please fill in all required fields: ${missingFields.join(', ')}`,
          variant: "destructive",
        });
        return;
      }
    }

    if (isDraft) {
      saveDraft();
      return;
    }

    // Generate ticket ID
    const ticketId = `TKT-${String(Date.now()).slice(-6)}`;

    toast({
      title: "Ticket Created Successfully",
      description: `Ticket ${ticketId} has been created successfully. Assignment will be determined based on category and sub-category.`,
    });

    // Clear form and navigate
    resetForm();
    navigate("/my-tickets");
  };

  const saveDraft = () => {
    const draftId = `DRAFT-${Date.now()}`;
    const newDraft: TicketDraft = {
      id: draftId,
      ...formData,
      requiredByDate: formData.requiredByDate?.toISOString(),
      attachments: formData.attachments.map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
      })),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const updatedDrafts = [...drafts, newDraft];
    setDrafts(updatedDrafts);
    localStorage.setItem('ticketDrafts', JSON.stringify(updatedDrafts));
    
    toast({
      title: "Draft Saved",
      description: "Your ticket has been saved as a draft",
    });
  };

  const loadDraft = (draft: TicketDraft) => {
    setFormData({
      title: draft.title,
      description: draft.description,
      project: draft.project,
      category: draft.category,
      subCategory: draft.subCategory,
      priority: draft.priority,
      onBehalfOf: draft.onBehalfOf,
      onBehalfOfType: draft.onBehalfOfType,
      location: draft.location,
      ticketType: draft.ticketType,
      requiredByDate: draft.requiredByDate ? new Date(draft.requiredByDate) : undefined,
      attachments: [], // Clear attachments, as they can't be restored from localStorage
      tasks: draft.tasks || [], // Load tasks from draft
      workLog: draft.workLog
    });

    toast({
      title: "Draft Loaded",
      description: "Draft data has been loaded. Please re-attach any files.",
      duration: 5000,
    });

    setShowDrafts(false);
  };

  const deleteDraft = (draftId: string) => {
    const updatedDrafts = drafts.filter(draft => draft.id !== draftId);
    setDrafts(updatedDrafts);
    localStorage.setItem('ticketDrafts', JSON.stringify(updatedDrafts));
    
    toast({
      title: "Draft Deleted",
      description: "Draft has been removed",
    });
  };

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      project: "",
      category: "",
      subCategory: "",
      priority: "",
      onBehalfOf: "",
      onBehalfOfType: "individual",
      location: "",
      ticketType: "service-request",
      attachments: [],
      tasks: [],
      workLog: "",
      requiredByDate: undefined,
    });
  };

  const handleOpenTaskDialog = (task: Task | null) => {
    if (task) {
      setEditingTask(task);
      setCurrentTaskData({ title: task.title, description: task.description, assignedTo: task.assignedTo, department: task.department });
    } else {
      setEditingTask(null);
      setCurrentTaskData({ title: "", description: "", assignedTo: "", department: "" });
    }
    setIsTaskDialogOpen(true);
  };

  const handleSaveTask = () => {
    if (!currentTaskData.title || !currentTaskData.assignedTo || !currentTaskData.department) {
      toast({
        title: "Task Error",
        description: "Task title, assignment, and department are required.",
        variant: "destructive",
      });
      return;
    }

    if (editingTask) {
      // Update existing task
      setFormData(prev => ({
        ...prev,
        tasks: prev.tasks.map(t => t.id === editingTask.id ? { ...t, ...currentTaskData } : t)
      }));
    } else {
      // Add new task
      const newTask: Task = {
        id: `TASK-${Date.now()}`,
        ...currentTaskData
      };
      setFormData(prev => ({ ...prev, tasks: [...prev.tasks, newTask] }));
    }
    setIsTaskDialogOpen(false);
  };

  const handleRemoveTask = (taskId: string) => {
    setFormData(prev => ({
      ...prev,
      tasks: prev.tasks.filter(t => t.id !== taskId)
    }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }));
  };

  const removeAttachment = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Create New Ticket</h1>
          <p className="text-muted-foreground">Submit a new request or report an issue</p>
        </div>
        <div className="flex gap-2">
          {drafts.length > 0 && (
            <Button variant="outline" onClick={() => setShowDrafts(!showDrafts)}>
              <FileText className="h-4 w-4 mr-2" />
              Drafts ({drafts.length})
            </Button>
          )}
          <Button variant="outline" onClick={() => handleSubmit(true)}>
            <Save className="h-4 w-4 mr-2" />
            Save Draft
          </Button>
          <Button onClick={() => handleSubmit(false)}>
            Submit Ticket
          </Button>
        </div>
      </div>

      {/* Draft Management Section */}
      {showDrafts && drafts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Saved Drafts</CardTitle>
            <CardDescription>Load a previously saved draft or delete unwanted drafts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {drafts.map((draft) => (
                <div key={draft.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium">{draft.title || "Untitled Draft"}</h4>
                    <p className="text-sm text-muted-foreground">
                      Created: {new Date(draft.createdAt).toLocaleDateString()} • 
                      Type: {draft.ticketType === "service-request" ? "Service Request" : "Service Incident"}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => loadDraft(draft)}>
                      Load
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => deleteDraft(draft.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Ticket Details</CardTitle>
          <CardDescription>
            Provide clear and detailed information about your request or issue
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Ticket Type */}
          <div className="space-y-3">
            <Label>Ticket Type *</Label>
            <RadioGroup 
              value={formData.ticketType} 
              onValueChange={(value: "service-request" | "service-incident") => 
                setFormData(prev => ({ ...prev, ticketType: value }))
              }
              className="flex gap-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="service-request" id="service-request" />
                <Label htmlFor="service-request">Service Request</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="service-incident" id="service-incident" />
                <Label htmlFor="service-incident">Service Incident / Concern</Label>
              </div>
            </RadioGroup>
          </div>

          <Separator />

          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Subject *</Label>
            <Input
              id="title"
              placeholder="Brief summary of the issue or request"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              placeholder="Detailed description of the issue, steps to reproduce, expected behavior, etc."
              className="min-h-32"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            />
          </div>

          {/* On Behalf Of */}
          <div className="space-y-4">
            <Label>On Behalf Of *</Label>
            <div className="space-y-3">
              <RadioGroup 
                value={formData.onBehalfOfType} 
                onValueChange={(value: "team" | "individual") => 
                  setFormData(prev => ({ ...prev, onBehalfOfType: value, onBehalfOf: "" }))
                }
                className="flex gap-6"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="individual" id="individual" />
                  <Label htmlFor="individual">Individual</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="team" id="team" />
                  <Label htmlFor="team">Team</Label>
                </div>
              </RadioGroup>
              
              <Select 
                value={formData.onBehalfOf} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, onBehalfOf: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={`Select a ${formData.onBehalfOfType}`} />
                </SelectTrigger>
                <SelectContent>
                  {(formData.onBehalfOfType === "team" ? teams : individuals).map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>



          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Location *</Label>
            <Input
              id="location"
              placeholder="Physical location or department (e.g., Building A, Floor 3, Room 305)"
              value={formData.location}
              onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
            />
          </div>

          {/* Project and Category */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Project *</Label>
              <Select 
                value={formData.project}
                onValueChange={(value) => setFormData(prev => ({ ...prev, project: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map((project) => (
                    <SelectItem key={project} value={project}>
                      {project}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Category *</Label>
              <Select 
                value={formData.category}
                onValueChange={(value) => setFormData(prev => ({ ...prev, category: value, subCategory: "" }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Sub Category */}
          <div className="space-y-2">
            <Label>Sub Category *</Label>
            <Select 
              value={formData.subCategory}
              onValueChange={(value) => setFormData(prev => ({ ...prev, subCategory: value }))}
              disabled={!formData.category}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a sub category" />
              </SelectTrigger>
              <SelectContent>
                {formData.category && subCategories[formData.category]?.map((subCategory) => (
                  <SelectItem key={subCategory} value={subCategory}>
                    {subCategory}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Priority */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
            <div className="space-y-2">
              <Label>Priority *</Label>
              <Select 
                value={formData.priority}
                onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
              >
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {priorities.map((priority) => (
                    <SelectItem key={priority} value={priority}>
                      {priority}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Required by Date */}
            <div className="space-y-2 md:space-y-0 md:flex md:items-center md:justify-around">
              <Label>Required by Date </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full md:w-48 justify-start text-left font-normal",
                      !formData.requiredByDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.requiredByDate ? format(formData.requiredByDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.requiredByDate}
                    onSelect={(date) => setFormData(prev => ({ ...prev, requiredByDate: date as Date | undefined }))}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* File Attachments */}
          <div className="space-y-2">
            <Label>Attachments</Label>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                  id="file-upload"
                  accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.log"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById('file-upload')?.click()}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Files
                </Button>
                <span className="text-sm text-muted-foreground">
                  Supported: Images, PDFs, Documents, Logs
                </span>
              </div>

              {formData.attachments.length > 0 && (
                <div className="space-y-2">
                  {formData.attachments.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <Paperclip className="h-4 w-4" />
                        <span className="text-sm">{file.name}</span>
                        <Badge variant="secondary" className="text-xs">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </Badge>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeAttachment(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Initial Work Log */}
          <div className="space-y-2">
            <Label htmlFor="worklog">Initial Work Log </Label>
            <Textarea
              id="worklog"
              placeholder="Any initial investigation, workarounds, or additional context..."
              className="min-h-20"
              value={formData.workLog}
              onChange={(e) => setFormData(prev => ({ ...prev, workLog: e.target.value }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* Associated Tasks Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Associated Tasks</CardTitle>
              <CardDescription>Add one or more tasks to be completed for this ticket.</CardDescription>
            </div>
            <Button type="button" variant="outline" onClick={() => handleOpenTaskDialog(null)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Task
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {formData.tasks.length > 0 ? (
            <div className="space-y-3">
              {formData.tasks.map((task) => (
                <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{task.title}</p>
                    <p className="text-sm text-muted-foreground">
                      Dept: {task.department} → Assigned to: {task.assignedTo}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="icon" onClick={() => handleOpenTaskDialog(task)}>
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => handleRemoveTask(task.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-sm text-muted-foreground py-4">
              No tasks have been added yet.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end gap-3">
        <Button variant="outline" onClick={() => navigate(-1)}>
          Cancel
        </Button>
        <Button variant="outline" onClick={() => handleSubmit(true)}>
          <Save className="h-4 w-4 mr-2" />
          Save Draft
        </Button>
        <Button onClick={() => handleSubmit(false)}>
          Submit Ticket
        </Button>
      </div>

      {/* Task Creation/Editing Dialog */}
      <Dialog open={isTaskDialogOpen} onOpenChange={setIsTaskDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editingTask ? "Edit Task" : "Add New Task"}</DialogTitle>
            <DialogDescription>
              Fill in the details for the task. It will be created once the ticket is submitted.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="task-title">Task Title</Label>
              <Input
                id="task-title"
                value={currentTaskData.title}
                onChange={(e) => setCurrentTaskData({ ...currentTaskData, title: e.target.value })}
                placeholder="e.g., 'Investigate server logs'"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="task-description">Description</Label>
              <Textarea
                id="task-description"
                value={currentTaskData.description}
                onChange={(e) => setCurrentTaskData({ ...currentTaskData, description: e.target.value })}
                placeholder="Provide a detailed description of the task."
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="task-department">Department</Label>
                <Select
                  value={currentTaskData.department}
                  onValueChange={(value) => setCurrentTaskData({ ...currentTaskData, department: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a department" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* Assuming 'teams' array holds department names */}
                    {teams.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="task-assignedTo">Assign To Group</Label>
                <Select
                  value={currentTaskData.assignedTo}
                  onValueChange={(value) => setCurrentTaskData({ ...currentTaskData, assignedTo: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a group" />
                  </SelectTrigger>
                  <SelectContent>
                    {groups.map((group) => (
                      <SelectItem key={group} value={group}>
                        {group}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTaskDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveTask}>Save Task</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}