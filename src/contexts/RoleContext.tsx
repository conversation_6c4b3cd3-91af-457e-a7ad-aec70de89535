import React, { createContext, useContext, useState, useEffect } from 'react';

export type UserRole = 'end-user' | 'back-office-agent' | 'administrator' | 'department-manager';

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  department?: string;
  permissions: string[];
}

interface RoleContextType {
  currentUser: UserProfile;
  switchRole: (role: UserRole) => void;
  hasPermission: (permission: string) => boolean;
}

const RoleContext = createContext<RoleContextType | undefined>(undefined);

// Mock user profiles for each role
const mockUsers: Record<UserRole, UserProfile> = {
  'end-user': {
    id: 'user-001',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'end-user',
    department: 'Marketing',
    permissions: ['create-ticket', 'view-own-tickets', 'edit-own-tickets']
  },
  'back-office-agent': {
    id: 'agent-001',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'back-office-agent',
    department: 'IT Support',
    permissions: [
      'create-ticket',
      'view-own-tickets',
      'view-assigned-tickets',
      'edit-assigned-tickets',
      'update-ticket-status',
      'add-worklogs',
      'view-department-tickets',
      'view-team-tickets',
      'assign-tickets',
      'reassign-tickets',
      'approve-tickets',
      'reject-tickets',
      'manage-team-workload',
      'manage-team',
      'edit-team-tickets'
    ]
  },
  'department-manager': {
    id: 'manager-001',
    name: 'Mike Wilson',
    email: '<EMAIL>',
    role: 'department-manager',
    department: 'IT Support',
    permissions: [
      'create-ticket',
      'view-own-tickets',
      'view-assigned-tickets', 
      'view-department-tickets',
      'view-team-tickets',
      'assign-tickets',
      'reassign-tickets',
      'approve-tickets',
      'reject-tickets',
      'manage-team-workload'
    ]
  },
  'administrator': {
    id: 'admin-001',
    name: 'Emily Davis',
    email: '<EMAIL>',
    role: 'administrator',
    department: 'IT Administration',
    permissions: [
      'create-ticket',
      'view-all-tickets',
      'edit-all-tickets',
      'delete-tickets',
      'assign-tickets',
      'reassign-tickets',
      'approve-tickets',
      'reject-tickets',
      'manage-users',
      'manage-roles',
      'view-analytics',
      'system-configuration',
      'manage-workflows',
      'bulk-operations'
    ]
  }
};

export function RoleProvider({ children }: { children: React.ReactNode }) {
  const [currentRole, setCurrentRole] = useState<UserRole>('end-user');

  // Load saved role from localStorage
  useEffect(() => {
    const savedRole = localStorage.getItem('demo-user-role') as UserRole;
    if (savedRole && mockUsers[savedRole]) {
      setCurrentRole(savedRole);
    }
  }, []);

  const switchRole = (role: UserRole) => {
    setCurrentRole(role);
    localStorage.setItem('demo-user-role', role);
  };

  const hasPermission = (permission: string): boolean => {
    return mockUsers[currentRole].permissions.includes(permission);
  };

  const currentUser = mockUsers[currentRole];

  return (
    <RoleContext.Provider value={{ currentUser, switchRole, hasPermission }}>
      {children}
    </RoleContext.Provider>
  );
}

export function useRole() {
  const context = useContext(RoleContext);
  if (context === undefined) {
    throw new Error('useRole must be used within a RoleProvider');
  }
  return context;
}

// Helper function to get role display name
export function getRoleDisplayName(role: UserRole): string {
  switch (role) {
    case 'end-user':
      return 'End User';
    case 'back-office-agent':
      return 'Back Office Agent';
    case 'department-manager':
      return 'Department Manager';
    case 'administrator':
      return 'Administrator';
    default:
      return 'Unknown Role';
  }
}

// Helper function to get role color
export function getRoleColor(role: UserRole): string {
  switch (role) {
    case 'end-user':
      return 'bg-blue-100 text-blue-800';
    case 'back-office-agent':
      return 'bg-green-100 text-green-800';
    case 'department-manager':
      return 'bg-purple-100 text-purple-800';
    case 'administrator':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}